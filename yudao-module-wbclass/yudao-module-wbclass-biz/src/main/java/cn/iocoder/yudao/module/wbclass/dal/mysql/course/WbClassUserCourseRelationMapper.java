package cn.iocoder.yudao.module.wbclass.dal.mysql.course;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassUserCourseRelationDO;
import cn.iocoder.yudao.module.wbclass.enums.UserCourseStatusEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 用户课程关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassUserCourseRelationMapper extends BaseMapperX<WbClassUserCourseRelationDO> {

    /**
     * 根据用户ID查询有效的课程关联列表
     *
     * @param userId 用户ID
     * @return 课程关联列表
     */
    default List<WbClassUserCourseRelationDO> selectValidListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<WbClassUserCourseRelationDO>()
                .eq(WbClassUserCourseRelationDO::getUserId, userId)
                .eq(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassUserCourseRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassUserCourseRelationDO::getExpireTime, LocalDateTime.now())
                )
                .orderByDesc(WbClassUserCourseRelationDO::getAcquireTime)
                .orderByDesc(WbClassUserCourseRelationDO::getId));
    }

    /**
     * 根据用户ID列表查询有效的课程关联列表
     *
     * @param userIds 用户ID列表
     * @return 课程关联列表
     */
    default List<WbClassUserCourseRelationDO> selectValidListByUserIds(Collection<Long> userIds) {
        return selectList(new LambdaQueryWrapperX<WbClassUserCourseRelationDO>()
                .in(WbClassUserCourseRelationDO::getUserId, userIds)
                .eq(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassUserCourseRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassUserCourseRelationDO::getExpireTime, LocalDateTime.now())
                )
                .orderByAsc(WbClassUserCourseRelationDO::getUserId)
                .orderByDesc(WbClassUserCourseRelationDO::getAcquireTime)
                .orderByDesc(WbClassUserCourseRelationDO::getId));
    }

    /**
     * 根据课程ID查询有效的用户关联列表
     *
     * @param courseId 课程ID
     * @return 用户关联列表
     */
    default List<WbClassUserCourseRelationDO> selectValidListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<WbClassUserCourseRelationDO>()
                .eq(WbClassUserCourseRelationDO::getCourseId, courseId)
                .eq(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassUserCourseRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassUserCourseRelationDO::getExpireTime, LocalDateTime.now())
                )
                .orderByDesc(WbClassUserCourseRelationDO::getAcquireTime)
                .orderByDesc(WbClassUserCourseRelationDO::getId));
    }

    /**
     * 检查用户是否拥有指定课程
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否拥有
     */
    default boolean existsValidByUserIdAndCourseId(Long userId, Long courseId) {
        return selectCount(new LambdaQueryWrapperX<WbClassUserCourseRelationDO>()
                .eq(WbClassUserCourseRelationDO::getUserId, userId)
                .eq(WbClassUserCourseRelationDO::getCourseId, courseId)
                .eq(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassUserCourseRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassUserCourseRelationDO::getExpireTime, LocalDateTime.now())
                )) > 0;
    }

    /**
     * 根据订单ID查询关联记录
     *
     * @param orderId 订单ID
     * @return 关联记录列表
     */
    default List<WbClassUserCourseRelationDO> selectListByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapperX<WbClassUserCourseRelationDO>()
                .eq(WbClassUserCourseRelationDO::getOrderId, orderId)
                .orderByDesc(WbClassUserCourseRelationDO::getId));
    }

    /**
     * 根据订单ID取消课程关联（软删除）
     *
     * @param orderId 订单ID
     */
    default void cancelByOrderId(Long orderId) {
        update(null, new LambdaUpdateWrapper<WbClassUserCourseRelationDO>()
                .eq(WbClassUserCourseRelationDO::getOrderId, orderId)
                .set(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.CANCELLED.getStatus()));
    }

    /**
     * 更新过期的课程状态
     */
    default void updateExpiredCourses() {
        update(null, new LambdaUpdateWrapper<WbClassUserCourseRelationDO>()
                .eq(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .isNotNull(WbClassUserCourseRelationDO::getExpireTime)
                .lt(WbClassUserCourseRelationDO::getExpireTime, LocalDateTime.now())
                .set(WbClassUserCourseRelationDO::getStatus, UserCourseStatusEnum.EXPIRED.getStatus()));
    }

}
