package cn.iocoder.yudao.module.wbclass.controller.admin.course;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.wbclass.controller.admin.vo.course.WbClassUserCourseRelationCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.vo.course.WbClassUserCourseRelationRespVO;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassUserCourseRelationConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassUserCourseRelationDO;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassUserCourseRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 用户课程关联
 *
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 用户课程关联")
@RestController
@RequestMapping("/wbclass/user-course-relation")
@Validated
public class WbClassUserCourseRelationController {

    @Resource
    private WbClassUserCourseRelationService userCourseRelationService;
    @Resource
    private WbClassCourseService courseService;

    @PostMapping("/create")
    @ApiOperation("创建用户课程关联")
    @PreAuthorize("@ss.hasPermission('wbclass:user-course-relation:create')")
    public CommonResult<Boolean> createUserCourseRelation(@Valid @RequestBody WbClassUserCourseRelationCreateReqVO createReqVO) {
        // 获取课程信息
        WbClassCourseDO course = courseService.getCourse(createReqVO.getCourseId());
        if (course == null) {
            return CommonResult.error(10002, "课程不存在");
        }

        // 根据获得方式创建关联
        switch (createReqVO.getAcquireType()) {
            case 2: // 赠送
                userCourseRelationService.addCourseByGift(
                    createReqVO.getUserId(),
                    createReqVO.getCourseId(),
                    course.getName(),
                    createReqVO.getExpireTime(),
                    createReqVO.getRemark()
                );
                break;
            case 3: // 活动
                userCourseRelationService.addCourseByActivity(
                    createReqVO.getUserId(),
                    createReqVO.getCourseId(),
                    course.getName(),
                    createReqVO.getExpireTime(),
                    createReqVO.getRemark()
                );
                break;
            case 4: // 管理员分配
                userCourseRelationService.addCourseByAdminAssign(
                    createReqVO.getUserId(),
                    createReqVO.getCourseId(),
                    course.getName(),
                    createReqVO.getExpireTime(),
                    createReqVO.getRemark()
                );
                break;
            default:
                return CommonResult.error(10002, "不支持的获得方式");
        }

        return success(true);
    }

    @GetMapping("/list-by-user")
    @ApiOperation("获得用户的课程关联列表")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('wbclass:user-course-relation:query')")
    public CommonResult<List<WbClassUserCourseRelationRespVO>> getUserCourseRelationList(@RequestParam("userId") Long userId) {
        List<WbClassUserCourseRelationDO> list = userCourseRelationService.getRelationsByUserId(userId);
        return success(WbClassUserCourseRelationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-by-course")
    @ApiOperation("获得课程的用户关联列表")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('wbclass:user-course-relation:query')")
    public CommonResult<List<WbClassUserCourseRelationRespVO>> getCourseUserRelationList(@RequestParam("courseId") Long courseId) {
        List<WbClassUserCourseRelationDO> list = userCourseRelationService.getUsersByCourseId(courseId);
        return success(WbClassUserCourseRelationConvert.INSTANCE.convertList(list));
    }

    @DeleteMapping("/cancel-by-order")
    @ApiOperation("根据订单ID取消课程关联")
    @ApiImplicitParam(name = "orderId", value = "订单ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('wbclass:user-course-relation:delete')")
    public CommonResult<Boolean> cancelCoursesByOrderId(@RequestParam("orderId") Long orderId) {
        userCourseRelationService.cancelCoursesByOrderId(orderId);
        return success(true);
    }

    @PostMapping("/update-expired")
    @ApiOperation("更新过期课程状态")
    @PreAuthorize("@ss.hasPermission('wbclass:user-course-relation:update')")
    public CommonResult<Boolean> updateExpiredCourses() {
        userCourseRelationService.updateExpiredCourses();
        return success(true);
    }

}
