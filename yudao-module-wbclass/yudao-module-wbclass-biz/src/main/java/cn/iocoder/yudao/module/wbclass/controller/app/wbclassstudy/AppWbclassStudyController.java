package cn.iocoder.yudao.module.wbclass.controller.app.wbclassstudy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.controller.app.vo.AppWbclassStudyRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseOrderMapper;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassOrderCourseRelationService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassUserCourseRelationService;
import cn.iocoder.yudao.module.wbclass.enums.PayStatusEnum;
import cn.iocoder.yudao.module.wbclass.enums.RefundStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Api(tags = "用户 APP - 学生提交记录")
@RestController
@RequestMapping("/edusys/wbclass-study")
@Validated
public class AppWbclassStudyController {

    @Resource
    private WbClassCourseOrderMapper courseOrderMapper;

    @Resource
    private WbClassOrderCourseRelationService orderCourseRelationService;
    @Resource
    private WbClassUserCourseRelationService userCourseRelationService;

    @GetMapping("/get-my-study-data")
    @ApiOperation("获取我的学习数据")
    @PreAuthenticated
    public CommonResult<List<AppWbclassStudyRespVO>> getMyStudyData() {
        // 创建学习数据列表
        List<AppWbclassStudyRespVO> studyDataList = new ArrayList<>();

        // 第一位：手动添加"我的作业"数据
        AppWbclassStudyRespVO homeworkData = new AppWbclassStudyRespVO();
        homeworkData.setId(0L);
        homeworkData.setName("我的作业");
        homeworkData.setType("homework");
        studyDataList.add(homeworkData);

        // 获取当前登录用户ID
        Long userId = SecurityFrameworkUtils.getLoginMemberId();
        if (userId != null) {
            // 使用新的用户课程关联服务获取用户拥有的课程
            List<WbClassCourseDO> purchasedCourses = userCourseRelationService.getCoursesByUserId(userId);

            if (!purchasedCourses.isEmpty()) {
                // 查询用户已支付且未退款的订单（用于获取SKU信息）
                List<WbClassCourseOrderDO> paidOrders = courseOrderMapper.selectList(
                    new LambdaQueryWrapperX<WbClassCourseOrderDO>()
                        .eq(WbClassCourseOrderDO::getUserId, userId)
                        .eq(WbClassCourseOrderDO::getPayStatus, PayStatusEnum.PAID.getStatus()) // 已支付
                        .ne(WbClassCourseOrderDO::getRefundStatus, RefundStatusEnum.REFUNDED.getStatus()) // 排除已退款
                        .orderByDesc(WbClassCourseOrderDO::getPayTime) // 按支付时间倒序
                );

                // 按课程ID分组订单（用于获取SKU信息）
                Map<Long, List<WbClassCourseOrderDO>> courseOrdersMap = paidOrders.stream()
                    .collect(Collectors.groupingBy(WbClassCourseOrderDO::getCourseId));

                // 为每个已购买的课程创建学习数据
                for (WbClassCourseDO course : purchasedCourses) {
                    AppWbclassStudyRespVO courseData = new AppWbclassStudyRespVO();
                    courseData.setId(course.getId());
                    courseData.setName(course.getName());
                    courseData.setType("course");

                    // 构建已购买的SKU列表
                    List<AppWbclassStudyRespVO.PurchasedSkuVO> purchasedSkus = new ArrayList<>();
                    List<WbClassCourseOrderDO> courseOrders = courseOrdersMap.get(course.getId());
                    if (courseOrders != null) {
                        for (WbClassCourseOrderDO order : courseOrders) {
                            AppWbclassStudyRespVO.PurchasedSkuVO skuVO = new AppWbclassStudyRespVO.PurchasedSkuVO();
                            skuVO.setOrderId(order.getId());
                            skuVO.setSkuId(order.getSkuId());
                            skuVO.setSkuName(order.getSkuName());
                            skuVO.setPayTime(order.getPayTime());
                            purchasedSkus.add(skuVO);
                        }
                    }

                    courseData.setPurchasedSkus(purchasedSkus);
                    studyDataList.add(courseData);
                }
            }
        }

        return success(studyDataList);
    }

}
