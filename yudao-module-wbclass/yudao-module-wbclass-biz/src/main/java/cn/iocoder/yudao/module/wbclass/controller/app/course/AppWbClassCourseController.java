package cn.iocoder.yudao.module.wbclass.controller.app.course;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.*;
import cn.iocoder.yudao.module.wbclass.controller.app.vo.AppWbClassUserCourseRespVO;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseConvert;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseOrderConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseOrderService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassUserCourseRelationService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static cn.hutool.extra.servlet.ServletUtil.getClientIP;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Api(tags = "用户App - 练习营课程商城")
@RestController
@RequestMapping("/wbclass/course")
@Validated
public class AppWbClassCourseController {

    @Resource
    private WbClassCourseService courseService;

    @Resource
    private WbClassCourseOrderService orderService;
    @Resource
    private WbClassUserCourseRelationService userCourseRelationService;

    @GetMapping("/page")
    @ApiOperation("获得课程产品分页")
    public CommonResult<PageResult<AppWbClassCourseRespVO>> getCoursePage(@Valid AppWbClassCoursePageReqVO pageVO) {
        PageResult<WbClassCourseDO> pageResult = courseService.getAppCoursePage(pageVO);
        return success(WbClassCourseConvert.INSTANCE.convertAppPage(pageResult));
    }

    @GetMapping("/get")
    @ApiOperation("获得课程产品详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<AppWbClassCourseRespVO> getCourse(@RequestParam("id") Long id) {
        WbClassCourseDO course = courseService.getCourse(id);
        return success(WbClassCourseConvert.INSTANCE.convertApp(course));
    }

    @PostMapping("/order/create")
    @ApiOperation("创建课程订单")
    @PreAuthenticated
    public CommonResult<Long> createOrder(@Valid @RequestBody AppWbClassCourseOrderCreateReqVO createReqVO,
                                          HttpServletRequest request) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String userIp = getClientIP(request);
        return success(orderService.createOrder(createReqVO, userId, userIp));
    }

    @PostMapping("/order/cancel")
    @ApiOperation("取消订单")
    @ApiImplicitParam(name = "id", value = "订单编号", required = true, dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<Boolean> cancelOrder(@RequestParam("id") Long id) {
        orderService.cancelOrder(id, 1); // 用户取消
        return success(true);
    }

    @GetMapping("/order/get")
    @ApiOperation("获得课程订单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<AppWbClassCourseOrderRespVO> getOrder(@RequestParam("id") Long id) {
        WbClassCourseOrderDO order = orderService.getOrder(id);
        return success(WbClassCourseOrderConvert.INSTANCE.convertApp(order));
    }

    @GetMapping("/order/page")
    @ApiOperation("获得我的课程订单分页")
    @PreAuthenticated
    public CommonResult<PageResult<AppWbClassCourseOrderRespVO>> getOrderPage(@Valid AppWbClassCourseOrderPageReqVO pageVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        PageResult<WbClassCourseOrderDO> pageResult = orderService.getAppOrderPage(pageVO, userId);
        return success(WbClassCourseOrderConvert.INSTANCE.convertAppPage(pageResult));
    }

    @GetMapping("/my-courses")
    @ApiOperation("获得我的课程列表")
    @PreAuthenticated
    public CommonResult<List<AppWbClassUserCourseRespVO>> getMyCourses() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 使用新的用户课程关联服务获取用户拥有的课程
        List<WbClassCourseDO> courses = userCourseRelationService.getCoursesByUserId(userId);
        // TODO: 需要创建转换器来转换为AppWbClassUserCourseRespVO
        // 这里暂时返回空列表，等待转换器实现
        return success(Lists.newArrayList());
    }

}
