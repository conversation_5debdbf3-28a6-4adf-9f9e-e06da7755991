package cn.iocoder.yudao.module.wbclass.convert.course;

import cn.iocoder.yudao.module.wbclass.controller.admin.vo.course.WbClassUserCourseRelationRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassUserCourseRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户课程关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassUserCourseRelationConvert {

    WbClassUserCourseRelationConvert INSTANCE = Mappers.getMapper(WbClassUserCourseRelationConvert.class);

    WbClassUserCourseRelationRespVO convert(WbClassUserCourseRelationDO bean);

    List<WbClassUserCourseRelationRespVO> convertList(List<WbClassUserCourseRelationDO> list);

}
