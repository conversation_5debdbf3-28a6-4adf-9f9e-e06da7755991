package cn.iocoder.yudao.module.wbclass.service.course;

import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassUserCourseRelationDO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 用户课程关联 Service 接口
 * 
 * 提供用户与课程关联关系的业务逻辑，直接管理用户拥有的课程
 *
 * <AUTHOR>
 */
public interface WbClassUserCourseRelationService {

    /**
     * 为用户添加课程（通过购买）
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param courseName 课程名称
     * @param orderId 订单ID
     * @param skuId SKU ID
     * @param productId 产品ID
     * @param expireTime 过期时间（NULL表示永久有效）
     * @param remark 备注
     */
    void addCourseByPurchase(Long userId, Long courseId, String courseName, Long orderId, 
                           Long skuId, Long productId, LocalDateTime expireTime, String remark);

    /**
     * 为用户添加课程（通过赠送）
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param courseName 课程名称
     * @param expireTime 过期时间（NULL表示永久有效）
     * @param remark 备注
     */
    void addCourseByGift(Long userId, Long courseId, String courseName, LocalDateTime expireTime, String remark);

    /**
     * 为用户添加课程（通过活动）
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param courseName 课程名称
     * @param expireTime 过期时间（NULL表示永久有效）
     * @param remark 备注
     */
    void addCourseByActivity(Long userId, Long courseId, String courseName, LocalDateTime expireTime, String remark);

    /**
     * 为用户添加课程（管理员分配）
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param courseName 课程名称
     * @param expireTime 过期时间（NULL表示永久有效）
     * @param remark 备注
     */
    void addCourseByAdminAssign(Long userId, Long courseId, String courseName, LocalDateTime expireTime, String remark);

    /**
     * 批量为用户添加课程（通过购买）
     *
     * @param userId 用户ID
     * @param courses 课程列表
     * @param orderId 订单ID
     * @param skuId SKU ID
     * @param productId 产品ID
     * @param expireTime 过期时间（NULL表示永久有效）
     */
    void addCoursesByPurchase(Long userId, List<WbClassCourseDO> courses, Long orderId, 
                            Long skuId, Long productId, LocalDateTime expireTime);

    /**
     * 根据用户ID获取拥有的课程列表
     *
     * @param userId 用户ID
     * @return 课程列表
     */
    List<WbClassCourseDO> getCoursesByUserId(Long userId);

    /**
     * 根据用户ID列表获取拥有的课程列表
     *
     * @param userIds 用户ID列表
     * @return 课程列表
     */
    List<WbClassCourseDO> getCoursesByUserIds(Collection<Long> userIds);

    /**
     * 根据课程ID获取拥有该课程的用户列表
     *
     * @param courseId 课程ID
     * @return 用户关联列表
     */
    List<WbClassUserCourseRelationDO> getUsersByCourseId(Long courseId);

    /**
     * 检查用户是否拥有指定课程
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否拥有
     */
    boolean hasUserCourse(Long userId, Long courseId);

    /**
     * 根据用户ID获取课程关联记录
     *
     * @param userId 用户ID
     * @return 关联记录列表
     */
    List<WbClassUserCourseRelationDO> getRelationsByUserId(Long userId);

    /**
     * 根据订单ID获取课程关联记录
     *
     * @param orderId 订单ID
     * @return 关联记录列表
     */
    List<WbClassUserCourseRelationDO> getRelationsByOrderId(Long orderId);

    /**
     * 根据订单ID取消课程关联（用于退款等场景）
     *
     * @param orderId 订单ID
     */
    void cancelCoursesByOrderId(Long orderId);

    /**
     * 更新过期的课程状态
     */
    void updateExpiredCourses();

}
