package cn.iocoder.yudao.module.wbclass.controller.admin.course;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.wbclass.controller.admin.vo.course.WbClassCourseUserRelationCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.vo.course.WbClassCourseUserRelationRespVO;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseUserRelationConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseUserRelationDO;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseUserRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 课程用户关联
 *
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 课程用户关联")
@RestController
@RequestMapping("/wbclass/course-user-relation")
@Validated
public class WbClassCourseUserRelationController {

    @Resource
    private WbClassCourseUserRelationService courseUserRelationService;
    @Resource
    private WbClassCourseService courseService;

    @PostMapping("/create")
    @ApiOperation("创建课程用户关联")
    @PreAuthorize("@ss.hasPermission('wbclass:course-user-relation:create')")
    public CommonResult<Boolean> createCourseUserRelation(@Valid @RequestBody WbClassCourseUserRelationCreateReqVO createReqVO) {
        // 获取课程信息
        WbClassCourseDO course = courseService.getCourse(createReqVO.getCourseId());
        if (course == null) {
            return CommonResult.error("课程不存在");
        }

        // 根据获得方式创建关联
        switch (createReqVO.getAcquireType()) {
            case 2: // 赠送
                courseUserRelationService.addCourseByGift(
                    createReqVO.getUserId(),
                    createReqVO.getCourseId(),
                    course.getName(),
                    createReqVO.getExpireTime(),
                    createReqVO.getRemark()
                );
                break;
            case 3: // 活动
                courseUserRelationService.addCourseByActivity(
                    createReqVO.getUserId(),
                    createReqVO.getCourseId(),
                    course.getName(),
                    createReqVO.getExpireTime(),
                    createReqVO.getRemark()
                );
                break;
            case 4: // 管理员分配
                courseUserRelationService.addCourseByAdminAssign(
                    createReqVO.getUserId(),
                    createReqVO.getCourseId(),
                    course.getName(),
                    createReqVO.getExpireTime(),
                    createReqVO.getRemark()
                );
                break;
            default:
                return CommonResult.error("不支持的获得方式");
        }

        return success(true);
    }

    @GetMapping("/list-by-user")
    @ApiOperation("获得用户的课程关联列表")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('wbclass:course-user-relation:query')")
    public CommonResult<List<WbClassCourseUserRelationRespVO>> getUserCourseRelationList(@RequestParam("userId") Long userId) {
        List<WbClassCourseUserRelationDO> list = courseUserRelationService.getRelationsByUserId(userId);
        return success(WbClassCourseUserRelationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-by-course")
    @ApiOperation("获得课程的用户关联列表")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('wbclass:course-user-relation:query')")
    public CommonResult<List<WbClassCourseUserRelationRespVO>> getCourseUserRelationList(@RequestParam("courseId") Long courseId) {
        List<WbClassCourseUserRelationDO> list = courseUserRelationService.getUsersByCourseId(courseId);
        return success(WbClassCourseUserRelationConvert.INSTANCE.convertList(list));
    }

    @DeleteMapping("/cancel-by-order")
    @ApiOperation("根据订单ID取消课程关联")
    @ApiImplicitParam(name = "orderId", value = "订单ID", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('wbclass:course-user-relation:delete')")
    public CommonResult<Boolean> cancelCoursesByOrderId(@RequestParam("orderId") Long orderId) {
        courseUserRelationService.cancelCoursesByOrderId(orderId);
        return success(true);
    }

    @PostMapping("/update-expired")
    @ApiOperation("更新过期课程状态")
    @PreAuthorize("@ss.hasPermission('wbclass:course-user-relation:update')")
    public CommonResult<Boolean> updateExpiredCourses() {
        courseUserRelationService.updateExpiredCourses();
        return success(true);
    }

}
