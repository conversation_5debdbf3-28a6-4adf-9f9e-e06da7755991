-- 数据迁移脚本：从订单-课程关联表迁移到用户-课程关联表

-- 1. 首先创建新表（如果还没有创建）
-- 参考 01-新表结构设计.sql

-- 2. 数据迁移：将现有的订单-课程关联数据迁移到新的用户-课程关联表
INSERT INTO `edusys_wbclass_user_course_relation` (
    `user_id`,
    `course_id`, 
    `course_name`,
    `acquire_type`,
    `acquire_time`,
    `order_id`,
    `sku_id`,
    `product_id`,
    `expire_time`,
    `status`,
    `remark`,
    `creator`,
    `create_time`,
    `updater`,
    `update_time`,
    `deleted`,
    `tenant_id`
)
SELECT 
    o.user_id,
    r.course_id,
    r.course_name,
    1 as acquire_type, -- 1表示购买获得
    COALESCE(o.pay_time, o.create_time) as acquire_time, -- 使用支付时间，如果没有则使用创建时间
    r.order_id,
    o.sku_id,
    o.product_id,
    NULL as expire_time, -- 暂时设为永久有效，后续可根据业务需求调整
    CASE 
        WHEN o.pay_status = 2 AND o.refund_status != 3 THEN 1 -- 已支付且未退款：有效
        WHEN o.refund_status = 3 THEN 3 -- 已退款：已取消
        ELSE 4 -- 其他情况：已删除
    END as status,
    CONCAT('从订单迁移，原订单号：', o.order_no) as remark,
    'SYSTEM' as creator,
    NOW() as create_time,
    'SYSTEM' as updater,
    NOW() as update_time,
    0 as deleted,
    COALESCE(o.tenant_id, 0) as tenant_id
FROM edusys_wbclass_course_order_relation r
INNER JOIN edusys_wbclass_course_order o ON r.order_id = o.id
WHERE r.status = 1 -- 只迁移正常状态的关联记录
  AND o.deleted = 0 -- 只迁移未删除的订单
  AND r.deleted = 0 -- 只迁移未删除的关联记录
ORDER BY o.user_id, r.course_id, o.pay_time DESC;

-- 3. 验证迁移结果
-- 检查迁移的记录数量
SELECT 
    '原订单-课程关联记录数' as description,
    COUNT(*) as count
FROM edusys_wbclass_course_order_relation r
INNER JOIN edusys_wbclass_course_order o ON r.order_id = o.id
WHERE r.status = 1 AND o.deleted = 0 AND r.deleted = 0

UNION ALL

SELECT 
    '新用户-课程关联记录数' as description,
    COUNT(*) as count
FROM edusys_wbclass_user_course_relation
WHERE deleted = 0;

-- 4. 检查是否有重复的用户-课程关联
SELECT 
    user_id,
    course_id,
    COUNT(*) as count
FROM edusys_wbclass_user_course_relation
WHERE deleted = 0
GROUP BY user_id, course_id
HAVING COUNT(*) > 1;

-- 5. 如果有重复记录，保留最新的一条（按获得时间排序）
-- 注意：这个操作需要谨慎执行，建议先备份数据
/*
DELETE t1 FROM edusys_wbclass_user_course_relation t1
INNER JOIN edusys_wbclass_user_course_relation t2 
WHERE t1.user_id = t2.user_id 
  AND t1.course_id = t2.course_id
  AND t1.deleted = 0 
  AND t2.deleted = 0
  AND (
    t1.acquire_time < t2.acquire_time 
    OR (t1.acquire_time = t2.acquire_time AND t1.id < t2.id)
  );
*/

-- 6. 创建备份表（可选，用于回滚）
CREATE TABLE `edusys_wbclass_course_order_relation_backup` AS 
SELECT * FROM `edusys_wbclass_course_order_relation`;

-- 7. 迁移完成后的验证查询
-- 验证用户课程数量是否正确
SELECT 
    o.user_id,
    COUNT(DISTINCT r.course_id) as old_course_count,
    (
        SELECT COUNT(DISTINCT course_id) 
        FROM edusys_wbclass_user_course_relation ucr 
        WHERE ucr.user_id = o.user_id 
          AND ucr.status = 1 
          AND ucr.deleted = 0
    ) as new_course_count
FROM edusys_wbclass_course_order o
INNER JOIN edusys_wbclass_course_order_relation r ON o.id = r.order_id
WHERE o.pay_status = 2 -- 已支付
  AND o.refund_status != 3 -- 未退款
  AND o.deleted = 0
  AND r.status = 1
  AND r.deleted = 0
GROUP BY o.user_id
HAVING old_course_count != new_course_count;

-- 8. 迁移后的清理工作（可选，建议在确认迁移成功后执行）
-- 注意：这些操作是不可逆的，请谨慎执行
/*
-- 重命名旧表
RENAME TABLE edusys_wbclass_course_order_relation TO edusys_wbclass_course_order_relation_old;

-- 或者删除旧表（非常危险，建议先备份）
-- DROP TABLE edusys_wbclass_course_order_relation;
*/
