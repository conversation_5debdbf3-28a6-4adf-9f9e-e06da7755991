# 用户课程关联表重构说明

## 重构背景

原来的 `edusys_wbclass_course_order_relation` 表是订单和课程的关联表，用户需要通过订单来间接查询拥有的课程。这种设计有以下问题：

1. **查询复杂**：需要先查订单，再查关联表，最后查课程
2. **业务耦合**：课程权限与订单状态强耦合
3. **扩展困难**：无法支持非购买方式获得的课程（如赠送、活动等）
4. **性能问题**：多表关联查询性能较差

## 重构方案

### 新表设计

创建 `edusys_wbclass_user_course_relation` 表，直接关联用户和课程：

```sql
CREATE TABLE `edusys_wbclass_user_course_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `course_name` varchar(255) NOT NULL COMMENT '课程名称（冗余字段，便于查询）',
  `acquire_type` tinyint NOT NULL DEFAULT '1' COMMENT '获得方式：1-购买，2-赠送，3-活动，4-管理员分配，5-其他',
  `acquire_time` datetime NOT NULL COMMENT '获得时间',
  `order_id` bigint DEFAULT NULL COMMENT '关联的订单ID（如果是通过购买获得）',
  `sku_id` bigint DEFAULT NULL COMMENT '关联的SKU ID（如果是通过购买获得）',
  `product_id` bigint DEFAULT NULL COMMENT '关联的产品ID（如果是通过购买获得）',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间（NULL表示永久有效）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-有效，2-已过期，3-已取消，4-已删除',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  -- 其他标准字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_course` (`user_id`, `course_id`, `deleted`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_acquire_type` (`acquire_type`),
  KEY `idx_status` (`status`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户课程关联表';
```

### 核心特性

1. **直接关联**：用户和课程直接关联，无需通过订单
2. **获得方式**：支持多种课程获得方式（购买、赠送、活动等）
3. **过期管理**：支持课程过期时间设置
4. **状态管理**：支持课程状态管理（有效、过期、取消等）
5. **向后兼容**：保留订单关联信息，支持数据追溯

## 代码结构

### 新增文件

1. **DO实体类**：`WbClassUserCourseRelationDO.java`
2. **枚举类**：
   - `CourseAcquireTypeEnum.java` - 课程获得方式枚举
   - `UserCourseStatusEnum.java` - 用户课程状态枚举
3. **Mapper接口**：`WbClassUserCourseRelationMapper.java`
4. **Service接口**：`WbClassUserCourseRelationService.java`
5. **Service实现**：`WbClassUserCourseRelationServiceImpl.java`
6. **Controller**：`WbClassUserCourseRelationController.java`
7. **VO类**：
   - `WbClassUserCourseRelationCreateReqVO.java`
   - `WbClassUserCourseRelationRespVO.java`
   - `AppWbClassUserCourseRespVO.java`
8. **Convert类**：`WbClassUserCourseRelationConvert.java`

### 修改文件

1. **订单服务**：`WbClassCourseOrderServiceImpl.java`
   - 在订单支付成功后创建用户课程关联
   - 在订单退款后取消用户课程关联
2. **订单课程关联服务**：`WbClassOrderCourseRelationServiceImpl.java`
   - 添加对新服务的支持，优先使用新服务
3. **学习Controller**：`AppWbclassStudyController.java`
   - 使用新的用户课程关联服务查询用户课程
4. **课程Controller**：`AppWbClassCourseController.java`
   - 添加新的API接口查询用户课程

## 数据迁移

### 迁移脚本

参考 `02-数据迁移脚本.sql`，主要步骤：

1. 创建新表
2. 从订单-课程关联表迁移数据到用户-课程关联表
3. 验证迁移结果
4. 处理重复数据
5. 备份原表

### 迁移策略

1. **渐进式迁移**：新旧系统并行运行，逐步切换
2. **数据一致性**：确保迁移过程中数据一致性
3. **回滚机制**：保留原表作为备份，支持快速回滚

## API变更

### 新增API

1. **管理后台**：
   - `POST /wbclass/user-course-relation/create` - 创建用户课程关联
   - `GET /wbclass/user-course-relation/list-by-user` - 获取用户课程列表
   - `GET /wbclass/user-course-relation/list-by-course` - 获取课程用户列表
   - `DELETE /wbclass/user-course-relation/cancel-by-order` - 取消订单相关课程
   - `POST /wbclass/user-course-relation/update-expired` - 更新过期课程

2. **用户端**：
   - `GET /wbclass/course/my-courses` - 获取我的课程列表

### 兼容性

- 原有API保持不变，内部实现优先使用新服务
- 支持新旧系统并行运行
- 提供降级机制，新服务异常时自动回退到旧服务

## 优势

1. **性能提升**：直接查询用户课程，减少多表关联
2. **业务解耦**：课程权限与订单状态解耦
3. **功能扩展**：支持多种课程获得方式
4. **数据清晰**：用户课程关系一目了然
5. **维护简单**：减少复杂的业务逻辑

## 注意事项

1. **数据一致性**：确保新旧数据的一致性
2. **性能监控**：监控新系统的性能表现
3. **错误处理**：完善异常处理和降级机制
4. **测试覆盖**：充分测试各种场景
5. **文档更新**：及时更新相关文档

## 后续计划

1. **完善转换器**：实现完整的数据转换逻辑
2. **前端适配**：前端页面适配新的API
3. **监控告警**：添加相关监控和告警
4. **性能优化**：根据实际使用情况进行性能优化
5. **旧表清理**：在确认新系统稳定后清理旧表
