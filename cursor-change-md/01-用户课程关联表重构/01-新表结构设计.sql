-- 用户课程关联表重构
-- 将原来的订单-课程关联表重构为直接的用户-课程关联表

-- 1. 创建新的用户课程关联表
CREATE TABLE `edusys_wbclass_user_course_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `course_name` varchar(255) NOT NULL COMMENT '课程名称（冗余字段，便于查询）',
  `acquire_type` tinyint NOT NULL DEFAULT '1' COMMENT '获得方式：1-购买，2-赠送，3-活动，4-管理员分配，5-其他',
  `acquire_time` datetime NOT NULL COMMENT '获得时间',
  `order_id` bigint DEFAULT NULL COMMENT '关联的订单ID（如果是通过购买获得）',
  `sku_id` bigint DEFAULT NULL COMMENT '关联的SKU ID（如果是通过购买获得）',
  `product_id` bigint DEFAULT NULL COMMENT '关联的产品ID（如果是通过购买获得）',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间（NULL表示永久有效）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-有效，2-已过期，3-已取消，4-已删除',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_course` (`user_id`, `course_id`, `deleted`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_acquire_type` (`acquire_type`),
  KEY `idx_status` (`status`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户课程关联表';

-- 2. 获得方式枚举说明
-- 1-购买：通过订单购买获得
-- 2-赠送：其他用户或管理员赠送
-- 3-活动：通过活动获得（如签到、抽奖等）
-- 4-管理员分配：管理员直接分配
-- 5-其他：其他方式获得

-- 3. 状态枚举说明
-- 1-有效：用户可以正常使用该课程
-- 2-已过期：课程已过期，用户无法使用
-- 3-已取消：课程权限被取消（如退款）
-- 4-已删除：软删除状态

-- 4. 索引说明
-- uk_user_course：用户和课程的唯一索引，防止重复关联
-- idx_user_id：用户ID索引，用于快速查询用户的课程
-- idx_course_id：课程ID索引，用于快速查询课程的用户
-- idx_acquire_type：获得方式索引，用于统计分析
-- idx_status：状态索引，用于过滤有效课程
-- idx_order_id：订单ID索引，用于关联订单信息
-- idx_tenant_id：租户ID索引，用于多租户隔离
